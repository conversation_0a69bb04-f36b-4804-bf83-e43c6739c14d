"""
👥 Customer Intelligence Service
Provides 360-degree customer profiles with semantic enrichment
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, field
import json

logger = logging.getLogger(__name__)

@dataclass
class CustomerProfile:
    """Comprehensive customer profile with semantic intelligence"""
    customer_id: str
    name: str
    email: str
    phone: Optional[str] = None
    
    # Communication history
    emails: List[Dict[str, Any]] = field(default_factory=list)
    transcriptions: List[Dict[str, Any]] = field(default_factory=list)
    communications_summary: Dict[str, Any] = field(default_factory=dict)
    
    # Business data
    service_history: List[Dict[str, Any]] = field(default_factory=list)
    equipment: List[Dict[str, Any]] = field(default_factory=list)
    financial_data: Dict[str, Any] = field(default_factory=dict)
    
    # Intelligence metrics
    customer_health_score: float = 75.0
    lifetime_value: float = 0.0
    satisfaction_score: float = 75.0
    risk_assessment: str = "low"
    churn_probability: float = 0.1
    
    # Semantic insights
    sentiment_trend: List[Dict[str, Any]] = field(default_factory=list)
    communication_patterns: Dict[str, Any] = field(default_factory=dict)
    equipment_insights: Dict[str, Any] = field(default_factory=dict)
    predictive_insights: Dict[str, Any] = field(default_factory=dict)
    
    # Metadata
    created_at: datetime = field(default_factory=datetime.now)
    last_updated: datetime = field(default_factory=datetime.now)
    last_contact: Optional[datetime] = None

@dataclass
class CustomerInsight:
    """Individual customer insight"""
    insight_id: str
    customer_id: str
    insight_type: str  # sentiment, equipment, financial, predictive
    title: str
    description: str
    confidence: float
    impact: str  # low, medium, high, critical
    recommended_actions: List[str]
    created_at: datetime = field(default_factory=datetime.now)

class CustomerIntelligenceService:
    """
    👥 Customer Intelligence Service
    
    Provides comprehensive 360-degree customer profiles by aggregating
    and analyzing data from emails, transcriptions, service history,
    equipment data, and financial records with semantic enrichment.
    """
    
    def __init__(self):
        self.customer_profiles: Dict[str, CustomerProfile] = {}
        self.customer_insights: Dict[str, List[CustomerInsight]] = {}
        self.intelligence_cache = {}
        
        # Mock database connections (in real implementation, use actual DB clients)
        self.db_connections = {
            "postgresql": None,  # Customer profiles, service history
            "mongodb": None,     # Raw emails, transcriptions
            "redis": None        # Cache and real-time data
        }
        
        logger.info("👥 Customer Intelligence Service initialized")
    
    async def get_customer_profile(self, customer_id: str) -> Optional[CustomerProfile]:
        """Get comprehensive customer profile"""
        try:
            # Check cache first
            if customer_id in self.customer_profiles:
                profile = self.customer_profiles[customer_id]
                # Update if data is older than 1 hour
                if (datetime.now() - profile.last_updated).total_seconds() > 3600:
                    await self._refresh_customer_profile(customer_id)
                return self.customer_profiles[customer_id]
            
            # Load from database
            profile = await self._load_customer_profile(customer_id)
            if profile:
                self.customer_profiles[customer_id] = profile
                return profile
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Failed to get customer profile {customer_id}: {e}")
            return None
    
    async def create_customer_profile(self, customer_data: Dict[str, Any]) -> CustomerProfile:
        """Create new customer profile"""
        try:
            customer_id = customer_data.get("customer_id") or f"CUST_{datetime.now().strftime('%Y%m%d%H%M%S')}"
            
            profile = CustomerProfile(
                customer_id=customer_id,
                name=customer_data.get("name", ""),
                email=customer_data.get("email", ""),
                phone=customer_data.get("phone")
            )
            
            # Perform initial intelligence analysis
            await self._analyze_customer_intelligence(profile)
            
            # Store in cache and database
            self.customer_profiles[customer_id] = profile
            await self._save_customer_profile(profile)
            
            logger.info(f"✅ Customer profile created: {customer_id}")
            return profile
            
        except Exception as e:
            logger.error(f"❌ Failed to create customer profile: {e}")
            raise
    
    async def update_customer_profile(self, customer_id: str, updates: Dict[str, Any]) -> bool:
        """Update customer profile with new data"""
        try:
            profile = await self.get_customer_profile(customer_id)
            if not profile:
                logger.warning(f"⚠️ Customer profile not found: {customer_id}")
                return False
            
            # Update basic information
            if "name" in updates:
                profile.name = updates["name"]
            if "email" in updates:
                profile.email = updates["email"]
            if "phone" in updates:
                profile.phone = updates["phone"]
            
            # Add new communications
            if "emails" in updates:
                profile.emails.extend(updates["emails"])
            if "transcriptions" in updates:
                profile.transcriptions.extend(updates["transcriptions"])
            
            # Add service history
            if "service_history" in updates:
                profile.service_history.extend(updates["service_history"])
            
            # Add equipment data
            if "equipment" in updates:
                profile.equipment.extend(updates["equipment"])
            
            # Update financial data
            if "financial_data" in updates:
                profile.financial_data.update(updates["financial_data"])
            
            # Refresh intelligence analysis
            await self._analyze_customer_intelligence(profile)
            
            profile.last_updated = datetime.now()
            await self._save_customer_profile(profile)
            
            logger.info(f"✅ Customer profile updated: {customer_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to update customer profile {customer_id}: {e}")
            return False
    
    async def add_communication(self, 
                              customer_id: str,
                              communication_data: Dict[str, Any],
                              semantic_analysis: Optional[Dict[str, Any]] = None) -> bool:
        """Add new communication with semantic analysis"""
        try:
            profile = await self.get_customer_profile(customer_id)
            if not profile:
                # Create new profile if customer doesn't exist
                profile = await self.create_customer_profile({
                    "customer_id": customer_id,
                    "name": communication_data.get("customer_name", "Unknown"),
                    "email": communication_data.get("customer_email", "")
                })
            
            communication_type = communication_data.get("type", "email")
            
            # Add communication to appropriate list
            communication_record = {
                "id": f"comm_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                "type": communication_type,
                "content": communication_data.get("content", ""),
                "timestamp": datetime.now().isoformat(),
                "semantic_analysis": semantic_analysis or {},
                **communication_data
            }
            
            if communication_type == "email":
                profile.emails.append(communication_record)
            elif communication_type == "transcription":
                profile.transcriptions.append(communication_record)
            
            # Update last contact
            profile.last_contact = datetime.now()
            
            # Refresh intelligence analysis
            await self._analyze_customer_intelligence(profile)
            
            # Generate insights from new communication
            if semantic_analysis:
                await self._generate_communication_insights(customer_id, communication_record, semantic_analysis)
            
            profile.last_updated = datetime.now()
            await self._save_customer_profile(profile)
            
            logger.info(f"✅ Communication added for customer {customer_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to add communication for customer {customer_id}: {e}")
            return False
    
    async def get_customer_insights(self, customer_id: str) -> List[CustomerInsight]:
        """Get customer insights"""
        return self.customer_insights.get(customer_id, [])
    
    async def search_customers(self, 
                             query: str,
                             filters: Optional[Dict[str, Any]] = None) -> List[CustomerProfile]:
        """Search customers with semantic intelligence"""
        try:
            results = []
            query_lower = query.lower()
            
            for profile in self.customer_profiles.values():
                # Basic text search
                if (query_lower in profile.name.lower() or 
                    query_lower in profile.email.lower() or
                    (profile.phone and query_lower in profile.phone)):
                    results.append(profile)
                    continue
                
                # Search in communications
                for email in profile.emails:
                    if query_lower in email.get("content", "").lower():
                        results.append(profile)
                        break
                
                for transcription in profile.transcriptions:
                    if query_lower in transcription.get("content", "").lower():
                        results.append(profile)
                        break
            
            # Apply filters
            if filters:
                results = self._apply_customer_filters(results, filters)
            
            # Sort by relevance (mock scoring)
            results.sort(key=lambda p: p.customer_health_score, reverse=True)
            
            logger.info(f"🔍 Customer search returned {len(results)} results")
            return results[:50]  # Limit to 50 results
            
        except Exception as e:
            logger.error(f"❌ Customer search failed: {e}")
            return []
    
    async def get_customer_analytics(self) -> Dict[str, Any]:
        """Get customer analytics dashboard data"""
        try:
            total_customers = len(self.customer_profiles)
            
            if total_customers == 0:
                return {
                    "total_customers": 0,
                    "average_health_score": 0,
                    "high_risk_customers": 0,
                    "recent_communications": 0
                }
            
            # Calculate metrics
            health_scores = [p.customer_health_score for p in self.customer_profiles.values()]
            satisfaction_scores = [p.satisfaction_score for p in self.customer_profiles.values()]
            churn_probabilities = [p.churn_probability for p in self.customer_profiles.values()]
            
            # Recent communications (last 7 days)
            recent_cutoff = datetime.now() - timedelta(days=7)
            recent_communications = 0
            for profile in self.customer_profiles.values():
                if profile.last_contact and profile.last_contact >= recent_cutoff:
                    recent_communications += 1
            
            # High risk customers (churn probability > 0.7)
            high_risk_customers = sum(1 for p in churn_probabilities if p > 0.7)
            
            # Customer segments
            segments = {
                "high_value": sum(1 for p in self.customer_profiles.values() if p.lifetime_value > 5000),
                "medium_value": sum(1 for p in self.customer_profiles.values() if 1000 <= p.lifetime_value <= 5000),
                "low_value": sum(1 for p in self.customer_profiles.values() if p.lifetime_value < 1000)
            }
            
            return {
                "total_customers": total_customers,
                "average_health_score": round(sum(health_scores) / len(health_scores), 1),
                "average_satisfaction": round(sum(satisfaction_scores) / len(satisfaction_scores), 1),
                "high_risk_customers": high_risk_customers,
                "recent_communications": recent_communications,
                "customer_segments": segments,
                "churn_risk_distribution": {
                    "low": sum(1 for p in churn_probabilities if p < 0.3),
                    "medium": sum(1 for p in churn_probabilities if 0.3 <= p <= 0.7),
                    "high": sum(1 for p in churn_probabilities if p > 0.7)
                }
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to get customer analytics: {e}")
            return {}
    
    async def _load_customer_profile(self, customer_id: str) -> Optional[CustomerProfile]:
        """Load customer profile from database"""
        # Mock implementation - in real system, query PostgreSQL
        # This would aggregate data from multiple tables:
        # - customers table for basic info
        # - communications table for emails/transcriptions
        # - service_orders table for service history
        # - equipment table for equipment data
        # - invoices table for financial data
        
        # For demo, create a mock profile
        if customer_id.startswith("DEMO_"):
            return CustomerProfile(
                customer_id=customer_id,
                name=f"Demo Customer {customer_id[-3:]}",
                email=f"demo{customer_id[-3:]}@example.com",
                phone="+48 123 456 789",
                customer_health_score=85.0,
                lifetime_value=3500.0,
                satisfaction_score=88.0,
                risk_assessment="low",
                churn_probability=0.15
            )
        
        return None
    
    async def _save_customer_profile(self, profile: CustomerProfile):
        """Save customer profile to database"""
        # Mock implementation - in real system, save to PostgreSQL
        logger.debug(f"💾 Saving customer profile: {profile.customer_id}")
    
    async def _refresh_customer_profile(self, customer_id: str):
        """Refresh customer profile data"""
        profile = await self._load_customer_profile(customer_id)
        if profile:
            self.customer_profiles[customer_id] = profile
    
    async def _analyze_customer_intelligence(self, profile: CustomerProfile):
        """Analyze customer intelligence and update metrics"""
        try:
            # Analyze communication patterns
            await self._analyze_communication_patterns(profile)
            
            # Calculate health score
            profile.customer_health_score = await self._calculate_health_score(profile)
            
            # Calculate satisfaction score
            profile.satisfaction_score = await self._calculate_satisfaction_score(profile)
            
            # Assess churn risk
            profile.churn_probability = await self._assess_churn_risk(profile)
            
            # Update risk assessment
            profile.risk_assessment = self._classify_risk_level(profile.churn_probability)
            
            # Generate predictive insights
            profile.predictive_insights = await self._generate_predictive_insights(profile)
            
        except Exception as e:
            logger.error(f"❌ Customer intelligence analysis failed: {e}")
    
    async def _analyze_communication_patterns(self, profile: CustomerProfile):
        """Analyze customer communication patterns"""
        total_communications = len(profile.emails) + len(profile.transcriptions)
        
        if total_communications == 0:
            profile.communication_patterns = {"frequency": "none", "preferred_channel": "unknown"}
            return
        
        # Calculate communication frequency
        if profile.last_contact:
            days_since_last = (datetime.now() - profile.last_contact).days
            if days_since_last <= 7:
                frequency = "high"
            elif days_since_last <= 30:
                frequency = "medium"
            else:
                frequency = "low"
        else:
            frequency = "none"
        
        # Determine preferred channel
        email_count = len(profile.emails)
        transcription_count = len(profile.transcriptions)
        
        if email_count > transcription_count:
            preferred_channel = "email"
        elif transcription_count > email_count:
            preferred_channel = "phone"
        else:
            preferred_channel = "mixed"
        
        profile.communication_patterns = {
            "frequency": frequency,
            "preferred_channel": preferred_channel,
            "total_communications": total_communications,
            "email_ratio": email_count / total_communications if total_communications > 0 else 0,
            "phone_ratio": transcription_count / total_communications if total_communications > 0 else 0
        }
    
    async def _calculate_health_score(self, profile: CustomerProfile) -> float:
        """Calculate customer health score"""
        score = 50.0  # Base score
        
        # Communication recency boost
        if profile.last_contact:
            days_since_last = (datetime.now() - profile.last_contact).days
            if days_since_last <= 30:
                score += 20
            elif days_since_last <= 90:
                score += 10
        
        # Service history boost
        if len(profile.service_history) > 0:
            score += min(len(profile.service_history) * 5, 20)
        
        # Financial data boost
        if profile.lifetime_value > 1000:
            score += 10
        
        return min(score, 100.0)
    
    async def _calculate_satisfaction_score(self, profile: CustomerProfile) -> float:
        """Calculate customer satisfaction score"""
        # Mock calculation based on sentiment analysis
        positive_sentiments = 0
        total_sentiments = 0
        
        for email in profile.emails:
            sentiment = email.get("semantic_analysis", {}).get("sentiment", {})
            if sentiment.get("label") == "positive":
                positive_sentiments += 1
            total_sentiments += 1
        
        for transcription in profile.transcriptions:
            sentiment = transcription.get("semantic_analysis", {}).get("sentiment", {})
            if sentiment.get("label") == "positive":
                positive_sentiments += 1
            total_sentiments += 1
        
        if total_sentiments == 0:
            return 75.0  # Default neutral score
        
        satisfaction_ratio = positive_sentiments / total_sentiments
        return 50 + (satisfaction_ratio * 50)  # Scale to 50-100
    
    async def _assess_churn_risk(self, profile: CustomerProfile) -> float:
        """Assess customer churn risk"""
        risk_factors = 0
        
        # No recent contact
        if not profile.last_contact or (datetime.now() - profile.last_contact).days > 90:
            risk_factors += 0.3
        
        # Low satisfaction
        if profile.satisfaction_score < 60:
            risk_factors += 0.4
        
        # No service history
        if len(profile.service_history) == 0:
            risk_factors += 0.2
        
        # Low lifetime value
        if profile.lifetime_value < 500:
            risk_factors += 0.1
        
        return min(risk_factors, 1.0)
    
    def _classify_risk_level(self, churn_probability: float) -> str:
        """Classify risk level based on churn probability"""
        if churn_probability >= 0.7:
            return "high"
        elif churn_probability >= 0.4:
            return "medium"
        else:
            return "low"
    
    async def _generate_predictive_insights(self, profile: CustomerProfile) -> Dict[str, Any]:
        """Generate predictive insights for customer"""
        insights = {}
        
        # Maintenance prediction
        if len(profile.equipment) > 0:
            insights["next_maintenance"] = {
                "probability": 0.75,
                "timeframe": "30-60 days",
                "equipment": profile.equipment[0].get("type", "HVAC System")
            }
        
        # Upsell opportunities
        if profile.lifetime_value > 2000:
            insights["upsell_opportunity"] = {
                "probability": 0.6,
                "recommended_service": "Premium Maintenance Plan",
                "potential_value": 800
            }
        
        # Service needs
        if profile.satisfaction_score < 70:
            insights["service_attention"] = {
                "priority": "high",
                "recommended_action": "Customer satisfaction follow-up",
                "urgency": "within 7 days"
            }
        
        return insights
    
    async def _generate_communication_insights(self, 
                                             customer_id: str,
                                             communication: Dict[str, Any],
                                             semantic_analysis: Dict[str, Any]):
        """Generate insights from new communication"""
        insights = []
        
        # Sentiment insight
        sentiment = semantic_analysis.get("sentiment", {})
        if sentiment.get("label") == "negative" and sentiment.get("score", 0) > 0.7:
            insight = CustomerInsight(
                insight_id=f"sentiment_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                customer_id=customer_id,
                insight_type="sentiment",
                title="Negative Sentiment Detected",
                description=f"Customer expressed negative sentiment in recent {communication['type']}",
                confidence=sentiment.get("score", 0),
                impact="high",
                recommended_actions=[
                    "Follow up with customer satisfaction survey",
                    "Schedule manager call",
                    "Review service quality"
                ]
            )
            insights.append(insight)
        
        # Urgency insight
        urgency_score = semantic_analysis.get("urgency_score", 0)
        if urgency_score > 0.8:
            insight = CustomerInsight(
                insight_id=f"urgency_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                customer_id=customer_id,
                insight_type="urgency",
                title="High Urgency Communication",
                description="Customer communication indicates urgent service need",
                confidence=urgency_score,
                impact="critical",
                recommended_actions=[
                    "Schedule immediate technician visit",
                    "Activate emergency response protocol",
                    "Contact customer within 2 hours"
                ]
            )
            insights.append(insight)
        
        # Store insights
        if customer_id not in self.customer_insights:
            self.customer_insights[customer_id] = []
        self.customer_insights[customer_id].extend(insights)
        
        # Keep only recent insights (last 100)
        self.customer_insights[customer_id] = self.customer_insights[customer_id][-100:]
    
    def _apply_customer_filters(self, customers: List[CustomerProfile], filters: Dict[str, Any]) -> List[CustomerProfile]:
        """Apply filters to customer list"""
        filtered = customers
        
        if "risk_level" in filters:
            risk_level = filters["risk_level"]
            filtered = [c for c in filtered if c.risk_assessment == risk_level]
        
        if "min_health_score" in filters:
            min_score = filters["min_health_score"]
            filtered = [c for c in filtered if c.customer_health_score >= min_score]
        
        if "min_lifetime_value" in filters:
            min_value = filters["min_lifetime_value"]
            filtered = [c for c in filtered if c.lifetime_value >= min_value]
        
        if "last_contact_days" in filters:
            days = filters["last_contact_days"]
            cutoff = datetime.now() - timedelta(days=days)
            filtered = [c for c in filtered if c.last_contact and c.last_contact >= cutoff]
        
        return filtered