#!/usr/bin/env python3
"""
🌐 HVAC CRM Flet Interface - Web Launcher
Launch the interface as a web application
"""

import flet as ft
import logging
from main import HVACCRMInterface

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Launch the HVAC CRM Flet interface as web app"""
    logger.info("🌐 Launching HVAC CRM Cosmic Interface (Web Version)")
    logger.info("=" * 60)
    
    app = HVACCRMInterface()
    
    # Launch as web application
    ft.app(
        target=app.main,
        view=ft.AppView.WEB_BROWSER,
        port=8550,
        host="0.0.0.0"
    )

if __name__ == "__main__":
    main()