"""
🔄 Data Pipeline Orchestrator
Coordinates data flow between all systems with semantic enrichment
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json

logger = logging.getLogger(__name__)

class PipelineStage(Enum):
    """Data pipeline stages"""
    INGESTION = "ingestion"
    PROCESSING = "processing"
    ENRICHMENT = "enrichment"
    VALIDATION = "validation"
    STORAGE = "storage"
    NOTIFICATION = "notification"

class DataSource(Enum):
    """Data sources"""
    EMAIL = "email"
    TRANSCRIPTION = "transcription"
    CUSTOMER_DATA = "customer_data"
    EQUIPMENT_DATA = "equipment_data"
    FINANCIAL_DATA = "financial_data"
    CALENDAR_DATA = "calendar_data"
    EXTERNAL_API = "external_api"

@dataclass
class PipelineTask:
    """Individual pipeline task"""
    task_id: str
    data_source: DataSource
    stage: PipelineStage
    input_data: Dict[str, Any]
    output_data: Optional[Dict[str, Any]] = None
    status: str = "pending"  # pending, running, completed, failed
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error: Optional[str] = None
    processing_time: float = 0.0

@dataclass
class DataFlow:
    """Complete data flow from source to destination"""
    flow_id: str
    name: str
    data_source: DataSource
    stages: List[PipelineStage]
    tasks: List[PipelineTask] = field(default_factory=list)
    status: str = "active"  # active, paused, completed, failed
    created_at: datetime = field(default_factory=datetime.now)
    last_execution: Optional[datetime] = None
    success_rate: float = 100.0

class DataPipelineOrchestrator:
    """
    🔄 Data Pipeline Orchestrator
    
    Coordinates the flow of data through the entire HVAC CRM system,
    ensuring semantic enrichment, validation, and proper storage
    across all data sources and destinations.
    """
    
    def __init__(self):
        self.data_flows: Dict[str, DataFlow] = {}
        self.active_tasks: Dict[str, PipelineTask] = {}
        self.completed_tasks: List[PipelineTask] = []
        self.is_running = False
        
        # Pipeline processors for each stage
        self.stage_processors = {
            PipelineStage.INGESTION: self._process_ingestion,
            PipelineStage.PROCESSING: self._process_data,
            PipelineStage.ENRICHMENT: self._enrich_data,
            PipelineStage.VALIDATION: self._validate_data,
            PipelineStage.STORAGE: self._store_data,
            PipelineStage.NOTIFICATION: self._send_notifications
        }
        
        # Data source handlers
        self.source_handlers = {
            DataSource.EMAIL: self._handle_email_data,
            DataSource.TRANSCRIPTION: self._handle_transcription_data,
            DataSource.CUSTOMER_DATA: self._handle_customer_data,
            DataSource.EQUIPMENT_DATA: self._handle_equipment_data,
            DataSource.FINANCIAL_DATA: self._handle_financial_data,
            DataSource.CALENDAR_DATA: self._handle_calendar_data,
            DataSource.EXTERNAL_API: self._handle_external_api_data
        }
        
        # Pipeline statistics
        self.stats = {
            "total_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "average_processing_time": 0.0,
            "data_sources_processed": {source.value: 0 for source in DataSource},
            "last_execution": None
        }
        
        # Initialize default data flows
        self._initialize_default_flows()
        
        logger.info("🔄 Data Pipeline Orchestrator initialized")
    
    def _initialize_default_flows(self):
        """Initialize default data flows"""
        # Email processing flow
        email_flow = DataFlow(
            flow_id="email_processing",
            name="Email Processing Pipeline",
            data_source=DataSource.EMAIL,
            stages=[
                PipelineStage.INGESTION,
                PipelineStage.PROCESSING,
                PipelineStage.ENRICHMENT,
                PipelineStage.VALIDATION,
                PipelineStage.STORAGE,
                PipelineStage.NOTIFICATION
            ]
        )
        self.data_flows["email_processing"] = email_flow
        
        # Transcription processing flow
        transcription_flow = DataFlow(
            flow_id="transcription_processing",
            name="Transcription Processing Pipeline",
            data_source=DataSource.TRANSCRIPTION,
            stages=[
                PipelineStage.INGESTION,
                PipelineStage.PROCESSING,
                PipelineStage.ENRICHMENT,
                PipelineStage.VALIDATION,
                PipelineStage.STORAGE,
                PipelineStage.NOTIFICATION
            ]
        )
        self.data_flows["transcription_processing"] = transcription_flow
        
        # Customer data synchronization flow
        customer_flow = DataFlow(
            flow_id="customer_sync",
            name="Customer Data Synchronization",
            data_source=DataSource.CUSTOMER_DATA,
            stages=[
                PipelineStage.INGESTION,
                PipelineStage.VALIDATION,
                PipelineStage.ENRICHMENT,
                PipelineStage.STORAGE
            ]
        )
        self.data_flows["customer_sync"] = customer_flow
        
        # Equipment monitoring flow
        equipment_flow = DataFlow(
            flow_id="equipment_monitoring",
            name="Equipment Data Monitoring",
            data_source=DataSource.EQUIPMENT_DATA,
            stages=[
                PipelineStage.INGESTION,
                PipelineStage.PROCESSING,
                PipelineStage.ENRICHMENT,
                PipelineStage.STORAGE
            ]
        )
        self.data_flows["equipment_monitoring"] = equipment_flow
        
        # Calendar integration flow
        calendar_flow = DataFlow(
            flow_id="calendar_integration",
            name="Calendar Data Integration",
            data_source=DataSource.CALENDAR_DATA,
            stages=[
                PipelineStage.INGESTION,
                PipelineStage.PROCESSING,
                PipelineStage.VALIDATION,
                PipelineStage.STORAGE
            ]
        )
        self.data_flows["calendar_integration"] = calendar_flow
    
    async def submit_data(self, 
                         data_source: DataSource,
                         data: Dict[str, Any],
                         flow_id: Optional[str] = None) -> str:
        """Submit data for processing through the pipeline"""
        try:
            # Determine flow
            if not flow_id:
                flow_id = self._get_default_flow_for_source(data_source)
            
            if flow_id not in self.data_flows:
                raise ValueError(f"Unknown flow: {flow_id}")
            
            flow = self.data_flows[flow_id]
            
            # Create tasks for each stage
            task_ids = []
            for stage in flow.stages:
                task_id = f"{flow_id}_{stage.value}_{datetime.now().strftime('%Y%m%d%H%M%S%f')}"
                
                task = PipelineTask(
                    task_id=task_id,
                    data_source=data_source,
                    stage=stage,
                    input_data=data.copy()
                )
                
                flow.tasks.append(task)
                task_ids.append(task_id)
            
            # Start processing first task
            if flow.tasks:
                first_task = flow.tasks[-len(flow.stages)]  # Get the first task of this submission
                await self._execute_task(first_task)
            
            logger.info(f"📥 Data submitted to pipeline: {flow_id} ({len(task_ids)} tasks)")
            return task_ids[0] if task_ids else ""
            
        except Exception as e:
            logger.error(f"❌ Failed to submit data to pipeline: {e}")
            raise
    
    def _get_default_flow_for_source(self, data_source: DataSource) -> str:
        """Get default flow for data source"""
        flow_mapping = {
            DataSource.EMAIL: "email_processing",
            DataSource.TRANSCRIPTION: "transcription_processing",
            DataSource.CUSTOMER_DATA: "customer_sync",
            DataSource.EQUIPMENT_DATA: "equipment_monitoring",
            DataSource.CALENDAR_DATA: "calendar_integration"
        }
        return flow_mapping.get(data_source, "email_processing")
    
    async def _execute_task(self, task: PipelineTask):
        """Execute individual pipeline task"""
        try:
            task.status = "running"
            task.started_at = datetime.now()
            self.active_tasks[task.task_id] = task
            
            # Get processor for this stage
            processor = self.stage_processors.get(task.stage)
            if not processor:
                raise ValueError(f"No processor for stage: {task.stage}")
            
            # Execute processor
            result = await processor(task)
            
            # Update task
            task.output_data = result
            task.status = "completed"
            task.completed_at = datetime.now()
            task.processing_time = (task.completed_at - task.started_at).total_seconds()
            
            # Move to completed tasks
            self.completed_tasks.append(task)
            if task.task_id in self.active_tasks:
                del self.active_tasks[task.task_id]
            
            # Update statistics
            self._update_stats(task, True)
            
            # Execute next task in flow if exists
            await self._execute_next_task_in_flow(task)
            
            logger.info(f"✅ Task completed: {task.task_id} ({task.processing_time:.2f}s)")
            
        except Exception as e:
            task.status = "failed"
            task.error = str(e)
            task.completed_at = datetime.now()
            
            if task.task_id in self.active_tasks:
                del self.active_tasks[task.task_id]
            
            self.completed_tasks.append(task)
            self._update_stats(task, False)
            
            logger.error(f"❌ Task failed: {task.task_id} - {e}")
    
    async def _execute_next_task_in_flow(self, completed_task: PipelineTask):
        """Execute next task in the same flow"""
        try:
            # Find the flow containing this task
            flow = None
            for f in self.data_flows.values():
                if any(t.task_id == completed_task.task_id for t in f.tasks):
                    flow = f
                    break
            
            if not flow:
                return
            
            # Find current task index in flow
            task_index = None
            for i, task in enumerate(flow.tasks):
                if task.task_id == completed_task.task_id:
                    task_index = i
                    break
            
            if task_index is None:
                return
            
            # Find next task in the same submission
            current_stage_index = flow.stages.index(completed_task.stage)
            if current_stage_index < len(flow.stages) - 1:
                # Look for next stage task with same timestamp pattern
                base_timestamp = completed_task.task_id.split('_')[-1]
                next_stage = flow.stages[current_stage_index + 1]
                
                for task in flow.tasks:
                    if (task.stage == next_stage and 
                        task.task_id.endswith(base_timestamp) and
                        task.status == "pending"):
                        
                        # Pass output from previous task as input
                        if completed_task.output_data:
                            task.input_data.update(completed_task.output_data)
                        
                        await self._execute_task(task)
                        break
            
        except Exception as e:
            logger.error(f"❌ Failed to execute next task: {e}")
    
    async def _process_ingestion(self, task: PipelineTask) -> Dict[str, Any]:
        """Process data ingestion stage"""
        try:
            # Get source-specific handler
            handler = self.source_handlers.get(task.data_source)
            if handler:
                result = await handler(task.input_data, "ingestion")
            else:
                result = {"raw_data": task.input_data, "ingested_at": datetime.now().isoformat()}
            
            return {
                "stage": "ingestion",
                "status": "completed",
                "data": result,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ Ingestion processing failed: {e}")
            raise
    
    async def _process_data(self, task: PipelineTask) -> Dict[str, Any]:
        """Process data processing stage"""
        try:
            # Get source-specific handler
            handler = self.source_handlers.get(task.data_source)
            if handler:
                result = await handler(task.input_data, "processing")
            else:
                result = {"processed_data": task.input_data, "processed_at": datetime.now().isoformat()}
            
            return {
                "stage": "processing",
                "status": "completed",
                "data": result,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ Data processing failed: {e}")
            raise
    
    async def _enrich_data(self, task: PipelineTask) -> Dict[str, Any]:
        """Process data enrichment stage"""
        try:
            # Apply semantic enrichment
            enriched_data = task.input_data.copy()
            
            # Add semantic analysis if text content exists
            text_content = self._extract_text_content(task.input_data)
            if text_content:
                semantic_analysis = await self._perform_semantic_analysis(text_content)
                enriched_data["semantic_analysis"] = semantic_analysis
            
            # Add metadata
            enriched_data["enrichment_metadata"] = {
                "enriched_at": datetime.now().isoformat(),
                "enrichment_version": "1.0",
                "data_source": task.data_source.value
            }
            
            return {
                "stage": "enrichment",
                "status": "completed",
                "data": enriched_data,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ Data enrichment failed: {e}")
            raise
    
    async def _validate_data(self, task: PipelineTask) -> Dict[str, Any]:
        """Process data validation stage"""
        try:
            validation_results = {
                "is_valid": True,
                "errors": [],
                "warnings": []
            }
            
            # Basic validation
            if not task.input_data:
                validation_results["is_valid"] = False
                validation_results["errors"].append("Empty data")
            
            # Source-specific validation
            if task.data_source == DataSource.EMAIL:
                if "sender" not in task.input_data:
                    validation_results["warnings"].append("Missing sender information")
                if "content" not in task.input_data:
                    validation_results["warnings"].append("Missing email content")
            
            elif task.data_source == DataSource.CUSTOMER_DATA:
                if "customer_id" not in task.input_data:
                    validation_results["errors"].append("Missing customer ID")
                    validation_results["is_valid"] = False
            
            return {
                "stage": "validation",
                "status": "completed",
                "validation_results": validation_results,
                "data": task.input_data,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ Data validation failed: {e}")
            raise
    
    async def _store_data(self, task: PipelineTask) -> Dict[str, Any]:
        """Process data storage stage"""
        try:
            # Determine storage destination based on data source
            storage_destinations = []
            
            if task.data_source in [DataSource.EMAIL, DataSource.TRANSCRIPTION]:
                # Store in MongoDB for raw data
                storage_destinations.append("mongodb")
                # Store processed data in PostgreSQL
                storage_destinations.append("postgresql")
                # Cache in Redis
                storage_destinations.append("redis")
            
            elif task.data_source == DataSource.CUSTOMER_DATA:
                # Store in PostgreSQL
                storage_destinations.append("postgresql")
                # Cache in Redis
                storage_destinations.append("redis")
            
            # Mock storage operations
            storage_results = {}
            for destination in storage_destinations:
                storage_results[destination] = {
                    "status": "stored",
                    "record_id": f"{destination}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    "stored_at": datetime.now().isoformat()
                }
            
            return {
                "stage": "storage",
                "status": "completed",
                "storage_results": storage_results,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ Data storage failed: {e}")
            raise
    
    async def _send_notifications(self, task: PipelineTask) -> Dict[str, Any]:
        """Process notification stage"""
        try:
            notifications_sent = []
            
            # Determine who to notify based on data source and content
            if task.data_source == DataSource.EMAIL:
                semantic_analysis = task.input_data.get("semantic_analysis", {})
                urgency_score = semantic_analysis.get("urgency_score", 0)
                
                if urgency_score > 0.8:
                    notifications_sent.append({
                        "type": "urgent_email",
                        "recipient": "<EMAIL>",
                        "message": "High urgency customer email received",
                        "sent_at": datetime.now().isoformat()
                    })
            
            elif task.data_source == DataSource.TRANSCRIPTION:
                notifications_sent.append({
                    "type": "transcription_ready",
                    "recipient": "<EMAIL>",
                    "message": "New transcription processed and ready for review",
                    "sent_at": datetime.now().isoformat()
                })
            
            return {
                "stage": "notification",
                "status": "completed",
                "notifications_sent": notifications_sent,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ Notification sending failed: {e}")
            raise
    
    def _extract_text_content(self, data: Dict[str, Any]) -> str:
        """Extract text content for semantic analysis"""
        text_parts = []
        
        # Common text fields
        for field in ["content", "body", "text", "description", "subject", "title"]:
            if field in data and isinstance(data[field], str):
                text_parts.append(data[field])
        
        return " ".join(text_parts)
    
    async def _perform_semantic_analysis(self, text: str) -> Dict[str, Any]:
        """Perform semantic analysis on text"""
        # Mock semantic analysis (in real implementation, use semantic framework)
        return {
            "sentiment": {"label": "neutral", "score": 0.5},
            "urgency_score": 0.3,
            "keywords": ["hvac", "service", "customer"],
            "language": "pl",
            "confidence": 0.85
        }
    
    # Source-specific handlers
    async def _handle_email_data(self, data: Dict[str, Any], stage: str) -> Dict[str, Any]:
        """Handle email data processing"""
        if stage == "ingestion":
            return {
                "email_id": data.get("email_id", "unknown"),
                "sender": data.get("sender", ""),
                "subject": data.get("subject", ""),
                "content": data.get("content", ""),
                "received_at": data.get("received_at", datetime.now().isoformat())
            }
        elif stage == "processing":
            return {
                "processed_content": data.get("content", "").strip(),
                "sender_domain": data.get("sender", "").split("@")[-1] if "@" in data.get("sender", "") else "",
                "word_count": len(data.get("content", "").split()),
                "has_attachments": len(data.get("attachments", [])) > 0
            }
        return data
    
    async def _handle_transcription_data(self, data: Dict[str, Any], stage: str) -> Dict[str, Any]:
        """Handle transcription data processing"""
        if stage == "ingestion":
            return {
                "transcription_id": data.get("transcription_id", "unknown"),
                "audio_file": data.get("audio_file", ""),
                "transcription_text": data.get("transcription_text", ""),
                "duration": data.get("duration", 0),
                "created_at": data.get("created_at", datetime.now().isoformat())
            }
        elif stage == "processing":
            text = data.get("transcription_text", "")
            return {
                "processed_text": text.strip(),
                "word_count": len(text.split()),
                "estimated_reading_time": len(text.split()) / 200,  # 200 words per minute
                "language_detected": "pl"
            }
        return data
    
    async def _handle_customer_data(self, data: Dict[str, Any], stage: str) -> Dict[str, Any]:
        """Handle customer data processing"""
        return data  # Customer data is usually already structured
    
    async def _handle_equipment_data(self, data: Dict[str, Any], stage: str) -> Dict[str, Any]:
        """Handle equipment data processing"""
        return data
    
    async def _handle_financial_data(self, data: Dict[str, Any], stage: str) -> Dict[str, Any]:
        """Handle financial data processing"""
        return data
    
    async def _handle_calendar_data(self, data: Dict[str, Any], stage: str) -> Dict[str, Any]:
        """Handle calendar data processing"""
        if stage == "processing":
            return {
                "event_id": data.get("event_id", "unknown"),
                "title": data.get("title", ""),
                "start_time": data.get("start_time", ""),
                "end_time": data.get("end_time", ""),
                "attendees": data.get("attendees", []),
                "location": data.get("location", ""),
                "is_hvac_related": any(term in data.get("title", "").lower() 
                                     for term in ["hvac", "klimatyzacja", "serwis", "naprawa"])
            }
        return data
    
    async def _handle_external_api_data(self, data: Dict[str, Any], stage: str) -> Dict[str, Any]:
        """Handle external API data processing"""
        return data
    
    def _update_stats(self, task: PipelineTask, success: bool):
        """Update pipeline statistics"""
        self.stats["total_tasks"] += 1
        self.stats["last_execution"] = datetime.now()
        
        if success:
            self.stats["completed_tasks"] += 1
            
            # Update average processing time
            total_completed = self.stats["completed_tasks"]
            current_avg = self.stats["average_processing_time"]
            self.stats["average_processing_time"] = (
                (current_avg * (total_completed - 1) + task.processing_time) / total_completed
            )
        else:
            self.stats["failed_tasks"] += 1
        
        # Update data source statistics
        self.stats["data_sources_processed"][task.data_source.value] += 1
    
    def get_pipeline_status(self) -> Dict[str, Any]:
        """Get comprehensive pipeline status"""
        return {
            "is_running": self.is_running,
            "active_flows": len([f for f in self.data_flows.values() if f.status == "active"]),
            "active_tasks": len(self.active_tasks),
            "completed_tasks": len(self.completed_tasks),
            "statistics": self.stats,
            "flows": {
                flow_id: {
                    "name": flow.name,
                    "status": flow.status,
                    "data_source": flow.data_source.value,
                    "stages": [stage.value for stage in flow.stages],
                    "total_tasks": len(flow.tasks),
                    "last_execution": flow.last_execution.isoformat() if flow.last_execution else None
                }
                for flow_id, flow in self.data_flows.items()
            }
        }
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get status of specific task"""
        # Check active tasks
        if task_id in self.active_tasks:
            task = self.active_tasks[task_id]
            return {
                "task_id": task.task_id,
                "status": task.status,
                "stage": task.stage.value,
                "data_source": task.data_source.value,
                "created_at": task.created_at.isoformat(),
                "started_at": task.started_at.isoformat() if task.started_at else None,
                "processing_time": (datetime.now() - task.started_at).total_seconds() if task.started_at else 0
            }
        
        # Check completed tasks
        for task in self.completed_tasks:
            if task.task_id == task_id:
                return {
                    "task_id": task.task_id,
                    "status": task.status,
                    "stage": task.stage.value,
                    "data_source": task.data_source.value,
                    "created_at": task.created_at.isoformat(),
                    "started_at": task.started_at.isoformat() if task.started_at else None,
                    "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                    "processing_time": task.processing_time,
                    "error": task.error
                }
        
        return None
    
    async def start_pipeline(self):
        """Start pipeline processing"""
        self.is_running = True
        logger.info("🚀 Data pipeline started")
    
    def stop_pipeline(self):
        """Stop pipeline processing"""
        self.is_running = False
        logger.info("⏹️ Data pipeline stopped")
    
    def clear_completed_tasks(self):
        """Clear completed tasks history"""
        self.completed_tasks.clear()
        logger.info("🧹 Completed tasks history cleared")