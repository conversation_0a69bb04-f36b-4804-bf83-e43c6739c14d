"""
📧 Email Processor Service
Real-time email processing with semantic analysis for HVAC CRM
"""

import asyncio
import logging
import imaplib
import email
import os
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
import json
import re

logger = logging.getLogger(__name__)

@dataclass
class EmailAccount:
    """Email account configuration"""
    email_address: str
    password: str
    imap_server: str
    imap_port: int = 993
    use_ssl: bool = True
    purpose: str = "general"  # "transcriptions" for dolores@, "customer_emails" for grzegorz@

@dataclass
class ProcessedEmail:
    """Processed email with semantic analysis"""
    email_id: str
    account: str
    sender: str
    recipient: str
    subject: str
    body: str
    attachments: List[Dict[str, Any]]
    received_date: datetime
    processed_date: datetime
    semantic_analysis: Optional[Dict[str, Any]] = None
    customer_id: Optional[str] = None
    priority: str = "medium"
    category: str = "general"

class EmailProcessorService:
    """
    📧 Email Processor Service
    
    Processes <NAME_EMAIL> (M4A transcriptions)
    and grz<PERSON><EMAIL> (customer emails) with semantic analysis
    and automatic customer profile integration.
    """
    
    def __init__(self):
        self.email_accounts = self._configure_email_accounts()
        self.is_monitoring = False
        self.processed_emails: List[ProcessedEmail] = []
        self.processing_callbacks: List[Callable] = []
        
        # Email processing statistics
        self.stats = {
            "total_processed": 0,
            "transcription_emails": 0,
            "customer_emails": 0,
            "attachments_processed": 0,
            "last_check": None,
            "errors": 0
        }
        
        logger.info("📧 Email Processor Service initialized")
    
    def _configure_email_accounts(self) -> Dict[str, EmailAccount]:
        """Configure email accounts for monitoring"""
        accounts = {}
        
        # Dolores account for M4A transcriptions
        accounts["dolores"] = EmailAccount(
            email_address="<EMAIL>",
            password=os.getenv("DOLORES_EMAIL_PASSWORD", "Blaeritipol1"),
            imap_server="mail.koldbringers.pl",
            imap_port=993,
            use_ssl=True,
            purpose="transcriptions"
        )
        
        # Grzegorz account for customer emails
        accounts["grzegorz"] = EmailAccount(
            email_address="<EMAIL>", 
            password=os.getenv("GRZEGORZ_EMAIL_PASSWORD", "Blaeritipol1"),
            imap_server="mail.koldbringers.pl",
            imap_port=993,
            use_ssl=True,
            purpose="customer_emails"
        )
        
        return accounts
    
    def add_processing_callback(self, callback: Callable[[ProcessedEmail], None]):
        """Add callback for when emails are processed"""
        self.processing_callbacks.append(callback)
    
    async def start_monitoring(self, check_interval: int = 60):
        """Start monitoring email accounts"""
        self.is_monitoring = True
        logger.info("🚀 Starting email monitoring...")
        
        while self.is_monitoring:
            try:
                await self._check_all_accounts()
                self.stats["last_check"] = datetime.now()
                await asyncio.sleep(check_interval)
                
            except Exception as e:
                logger.error(f"❌ Email monitoring error: {e}")
                self.stats["errors"] += 1
                await asyncio.sleep(check_interval)
    
    def stop_monitoring(self):
        """Stop email monitoring"""
        self.is_monitoring = False
        logger.info("⏹️ Email monitoring stopped")
    
    async def _check_all_accounts(self):
        """Check all configured email accounts"""
        for account_name, account in self.email_accounts.items():
            try:
                await self._check_account(account_name, account)
            except Exception as e:
                logger.error(f"❌ Error checking account {account_name}: {e}")
    
    async def _check_account(self, account_name: str, account: EmailAccount):
        """Check specific email account for new messages"""
        try:
            # Connect to IMAP server
            if account.use_ssl:
                mail = imaplib.IMAP4_SSL(account.imap_server, account.imap_port)
            else:
                mail = imaplib.IMAP4(account.imap_server, account.imap_port)
            
            # Login
            mail.login(account.email_address, account.password)
            mail.select('INBOX')
            
            # Search for unread emails from last 24 hours
            since_date = (datetime.now() - timedelta(days=1)).strftime("%d-%b-%Y")
            search_criteria = f'(UNSEEN SINCE "{since_date}")'
            
            status, messages = mail.search(None, search_criteria)
            
            if status == 'OK' and messages[0]:
                email_ids = messages[0].split()
                logger.info(f"📬 Found {len(email_ids)} new emails in {account_name}")
                
                for email_id in email_ids[-10:]:  # Process last 10 emails
                    await self._process_email(mail, email_id, account_name, account)
            
            mail.close()
            mail.logout()
            
        except Exception as e:
            logger.error(f"❌ IMAP connection error for {account_name}: {e}")
            # For development, create mock emails
            await self._create_mock_email(account_name, account)
    
    async def _process_email(self, mail, email_id: bytes, account_name: str, account: EmailAccount):
        """Process individual email"""
        try:
            # Fetch email
            status, msg_data = mail.fetch(email_id, '(RFC822)')
            if status != 'OK':
                return
            
            # Parse email
            email_message = email.message_from_bytes(msg_data[0][1])
            
            # Extract email details
            sender = email_message.get('From', '')
            recipient = email_message.get('To', '')
            subject = email_message.get('Subject', '')
            date_str = email_message.get('Date', '')
            
            # Parse date
            try:
                received_date = email.utils.parsedate_to_datetime(date_str)
            except:
                received_date = datetime.now()
            
            # Extract body
            body = self._extract_email_body(email_message)
            
            # Extract attachments
            attachments = self._extract_attachments(email_message, account.purpose)
            
            # Create processed email
            processed_email = ProcessedEmail(
                email_id=f"{account_name}_{email_id.decode()}",
                account=account_name,
                sender=sender,
                recipient=recipient,
                subject=subject,
                body=body,
                attachments=attachments,
                received_date=received_date,
                processed_date=datetime.now()
            )
            
            # Perform semantic analysis
            await self._analyze_email_semantics(processed_email, account)
            
            # Identify customer
            await self._identify_customer(processed_email)
            
            # Categorize and prioritize
            await self._categorize_email(processed_email, account)
            
            # Store processed email
            self.processed_emails.append(processed_email)
            self.stats["total_processed"] += 1
            
            if account.purpose == "transcriptions":
                self.stats["transcription_emails"] += 1
            else:
                self.stats["customer_emails"] += 1
            
            if attachments:
                self.stats["attachments_processed"] += len(attachments)
            
            # Notify callbacks
            for callback in self.processing_callbacks:
                try:
                    callback(processed_email)
                except Exception as e:
                    logger.error(f"❌ Callback error: {e}")
            
            logger.info(f"✅ Processed email: {subject[:50]}...")
            
        except Exception as e:
            logger.error(f"❌ Email processing error: {e}")
    
    def _extract_email_body(self, email_message) -> str:
        """Extract email body text"""
        body = ""
        
        if email_message.is_multipart():
            for part in email_message.walk():
                content_type = part.get_content_type()
                content_disposition = str(part.get("Content-Disposition"))
                
                if content_type == "text/plain" and "attachment" not in content_disposition:
                    try:
                        body = part.get_payload(decode=True).decode('utf-8')
                        break
                    except:
                        continue
        else:
            try:
                body = email_message.get_payload(decode=True).decode('utf-8')
            except:
                body = str(email_message.get_payload())
        
        return body.strip()
    
    def _extract_attachments(self, email_message, purpose: str) -> List[Dict[str, Any]]:
        """Extract email attachments"""
        attachments = []
        
        if email_message.is_multipart():
            for part in email_message.walk():
                content_disposition = str(part.get("Content-Disposition"))
                
                if "attachment" in content_disposition:
                    filename = part.get_filename()
                    if filename:
                        # For transcription emails, focus on M4A files
                        if purpose == "transcriptions" and filename.lower().endswith('.m4a'):
                            attachment_data = {
                                "filename": filename,
                                "content_type": part.get_content_type(),
                                "size": len(part.get_payload(decode=True)) if part.get_payload(decode=True) else 0,
                                "type": "audio_transcription",
                                "needs_processing": True
                            }
                            attachments.append(attachment_data)
                        
                        # For customer emails, handle documents
                        elif purpose == "customer_emails" and any(filename.lower().endswith(ext) for ext in ['.pdf', '.doc', '.docx', '.jpg', '.png']):
                            attachment_data = {
                                "filename": filename,
                                "content_type": part.get_content_type(),
                                "size": len(part.get_payload(decode=True)) if part.get_payload(decode=True) else 0,
                                "type": "document",
                                "needs_processing": filename.lower().endswith('.pdf')
                            }
                            attachments.append(attachment_data)
        
        return attachments
    
    async def _analyze_email_semantics(self, processed_email: ProcessedEmail, account: EmailAccount):
        """Perform semantic analysis on email content"""
        try:
            # Mock semantic analysis (in real implementation, use semantic framework)
            text_to_analyze = f"{processed_email.subject} {processed_email.body}"
            
            # Basic sentiment analysis
            negative_words = ["problem", "issue", "broken", "not working", "urgent", "emergency", "complaint"]
            positive_words = ["thank", "great", "excellent", "satisfied", "good", "working well"]
            
            negative_count = sum(1 for word in negative_words if word in text_to_analyze.lower())
            positive_count = sum(1 for word in positive_words if word in text_to_analyze.lower())
            
            if negative_count > positive_count:
                sentiment = {"label": "negative", "score": 0.7}
            elif positive_count > negative_count:
                sentiment = {"label": "positive", "score": 0.8}
            else:
                sentiment = {"label": "neutral", "score": 0.5}
            
            # Urgency detection
            urgent_words = ["urgent", "emergency", "asap", "immediately", "critical", "broken"]
            urgency_score = min(sum(1 for word in urgent_words if word in text_to_analyze.lower()) * 0.3, 1.0)
            
            # HVAC concept extraction
            hvac_concepts = []
            hvac_terms = ["air conditioning", "heating", "hvac", "compressor", "filter", "maintenance", "repair", "installation"]
            for term in hvac_terms:
                if term in text_to_analyze.lower():
                    hvac_concepts.append(term.replace(" ", "_"))
            
            # Intent classification
            if account.purpose == "transcriptions":
                intent = {"label": "service_call", "confidence": 0.9}
            elif any(word in text_to_analyze.lower() for word in ["quote", "estimate", "price"]):
                intent = {"label": "quote_request", "confidence": 0.8}
            elif any(word in text_to_analyze.lower() for word in ["schedule", "appointment", "visit"]):
                intent = {"label": "scheduling", "confidence": 0.8}
            elif any(word in text_to_analyze.lower() for word in ["problem", "issue", "broken"]):
                intent = {"label": "service_request", "confidence": 0.9}
            else:
                intent = {"label": "general_inquiry", "confidence": 0.6}
            
            processed_email.semantic_analysis = {
                "sentiment": sentiment,
                "urgency_score": urgency_score,
                "intent": intent,
                "hvac_concepts": hvac_concepts,
                "keywords": self._extract_keywords(text_to_analyze),
                "language": "pl",  # Polish
                "confidence": 0.85
            }
            
        except Exception as e:
            logger.error(f"❌ Semantic analysis error: {e}")
            processed_email.semantic_analysis = {"error": str(e)}
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract keywords from text"""
        # Simple keyword extraction
        words = re.findall(r'\b\w+\b', text.lower())
        
        # Filter out common words
        stop_words = {"the", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by", "a", "an", "is", "are", "was", "were", "be", "been", "have", "has", "had", "do", "does", "did", "will", "would", "could", "should", "may", "might", "can", "i", "you", "he", "she", "it", "we", "they", "this", "that", "these", "those"}
        
        keywords = [word for word in words if len(word) > 3 and word not in stop_words]
        
        # Return top 10 most relevant keywords
        return list(set(keywords))[:10]
    
    async def _identify_customer(self, processed_email: ProcessedEmail):
        """Identify customer from email"""
        try:
            # Extract email address from sender
            email_match = re.search(r'[\w\.-]+@[\w\.-]+\.\w+', processed_email.sender)
            if email_match:
                sender_email = email_match.group()
                
                # Mock customer identification (in real implementation, query customer database)
                if "test" in sender_email or "demo" in sender_email:
                    processed_email.customer_id = f"CUST_{sender_email.split('@')[0].upper()}"
                else:
                    # Generate customer ID based on email
                    processed_email.customer_id = f"CUST_{hash(sender_email) % 10000:04d}"
            
        except Exception as e:
            logger.error(f"❌ Customer identification error: {e}")
    
    async def _categorize_email(self, processed_email: ProcessedEmail, account: EmailAccount):
        """Categorize and prioritize email"""
        try:
            # Set category based on account purpose and content
            if account.purpose == "transcriptions":
                processed_email.category = "transcription"
                processed_email.priority = "high"  # Transcriptions are usually important
            else:
                semantic_analysis = processed_email.semantic_analysis or {}
                intent = semantic_analysis.get("intent", {}).get("label", "general")
                urgency_score = semantic_analysis.get("urgency_score", 0)
                
                # Set category based on intent
                if intent == "service_request":
                    processed_email.category = "service"
                elif intent == "quote_request":
                    processed_email.category = "sales"
                elif intent == "scheduling":
                    processed_email.category = "scheduling"
                else:
                    processed_email.category = "general"
                
                # Set priority based on urgency
                if urgency_score > 0.8:
                    processed_email.priority = "critical"
                elif urgency_score > 0.5:
                    processed_email.priority = "high"
                elif urgency_score > 0.2:
                    processed_email.priority = "medium"
                else:
                    processed_email.priority = "low"
            
        except Exception as e:
            logger.error(f"❌ Email categorization error: {e}")
    
    async def _create_mock_email(self, account_name: str, account: EmailAccount):
        """Create mock email for development"""
        if account.purpose == "transcriptions":
            # Mock transcription email
            processed_email = ProcessedEmail(
                email_id=f"mock_{account_name}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                account=account_name,
                sender="<EMAIL>",
                recipient=account.email_address,
                subject="Nagranie rozmowy z klientem - 2025-05-30",
                body="Automatyczne nagranie rozmowy telefonicznej z klientem. Załącznik zawiera plik audio M4A.",
                attachments=[{
                    "filename": f"call_recording_{datetime.now().strftime('%Y%m%d_%H%M%S')}.m4a",
                    "content_type": "audio/m4a",
                    "size": 2048000,
                    "type": "audio_transcription",
                    "needs_processing": True
                }],
                received_date=datetime.now(),
                processed_date=datetime.now(),
                category="transcription",
                priority="high"
            )
        else:
            # Mock customer email
            processed_email = ProcessedEmail(
                email_id=f"mock_{account_name}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                account=account_name,
                sender="<EMAIL>",
                recipient=account.email_address,
                subject="Prośba o serwis klimatyzacji",
                body="Dzień dobry, mam problem z klimatyzacją. Przestała chłodzić i wydaje dziwne dźwięki. Proszę o kontakt w sprawie serwisu.",
                attachments=[],
                received_date=datetime.now(),
                processed_date=datetime.now(),
                category="service",
                priority="medium"
            )
        
        # Add semantic analysis
        await self._analyze_email_semantics(processed_email, account)
        await self._identify_customer(processed_email)
        
        # Store and notify
        self.processed_emails.append(processed_email)
        self.stats["total_processed"] += 1
        
        if account.purpose == "transcriptions":
            self.stats["transcription_emails"] += 1
        else:
            self.stats["customer_emails"] += 1
        
        # Notify callbacks
        for callback in self.processing_callbacks:
            try:
                callback(processed_email)
            except Exception as e:
                logger.error(f"❌ Callback error: {e}")
        
        logger.info(f"📧 Created mock email for {account_name}")
    
    def get_recent_emails(self, limit: int = 50) -> List[ProcessedEmail]:
        """Get recent processed emails"""
        return sorted(self.processed_emails, key=lambda e: e.processed_date, reverse=True)[:limit]
    
    def get_emails_by_category(self, category: str) -> List[ProcessedEmail]:
        """Get emails by category"""
        return [email for email in self.processed_emails if email.category == category]
    
    def get_emails_by_priority(self, priority: str) -> List[ProcessedEmail]:
        """Get emails by priority"""
        return [email for email in self.processed_emails if email.priority == priority]
    
    def get_emails_by_customer(self, customer_id: str) -> List[ProcessedEmail]:
        """Get emails for specific customer"""
        return [email for email in self.processed_emails if email.customer_id == customer_id]
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get email processing statistics"""
        return {
            **self.stats,
            "total_emails": len(self.processed_emails),
            "accounts_monitored": len(self.email_accounts),
            "is_monitoring": self.is_monitoring,
            "last_check_formatted": self.stats["last_check"].strftime("%Y-%m-%d %H:%M:%S") if self.stats["last_check"] else "Never"
        }
    
    def search_emails(self, query: str, filters: Optional[Dict[str, Any]] = None) -> List[ProcessedEmail]:
        """Search emails by content"""
        results = []
        query_lower = query.lower()
        
        for email in self.processed_emails:
            # Search in subject and body
            if (query_lower in email.subject.lower() or 
                query_lower in email.body.lower() or
                query_lower in email.sender.lower()):
                results.append(email)
        
        # Apply filters
        if filters:
            if "category" in filters:
                results = [e for e in results if e.category == filters["category"]]
            if "priority" in filters:
                results = [e for e in results if e.priority == filters["priority"]]
            if "account" in filters:
                results = [e for e in results if e.account == filters["account"]]
            if "date_from" in filters:
                date_from = datetime.fromisoformat(filters["date_from"])
                results = [e for e in results if e.received_date >= date_from]
            if "date_to" in filters:
                date_to = datetime.fromisoformat(filters["date_to"])
                results = [e for e in results if e.received_date <= date_to]
        
        return sorted(results, key=lambda e: e.received_date, reverse=True)