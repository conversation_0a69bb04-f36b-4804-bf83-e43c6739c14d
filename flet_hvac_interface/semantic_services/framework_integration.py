"""
🔗 Semantic Framework Integration Manager
Bridges Gobeklitepe semantic framework with Flet interface
"""

import asyncio
import logging
import sys
import os
from typing import Dict, List, Optional, Any
from datetime import datetime
from dataclasses import dataclass

# Add Gobeklitepe to path
sys.path.append(os.path.join(os.path.dirname(__file__), '../../Gobeklitepe'))

try:
    from hvac_semantic_framework.core.hvac_semantic_framework import HVACSemanticFramework, FrameworkConfig
    from hvac_semantic_framework.agents.agent_orchestrator import AgentType, TaskPriority
except ImportError as e:
    logging.warning(f"Gobeklitepe framework not available: {e}")
    # Create mock classes for development
    class HVACSemanticFramework:
        def __init__(self, config): pass
        async def initialize(self): return True
        async def analyze_customer_communication(self, *args, **kwargs): return {"success": True, "mock": True}
        async def search_hvac_knowledge(self, *args, **kwargs): return {"success": True, "mock": True}
        async def process_equipment_data(self, *args, **kwargs): return {"success": True, "mock": True}
        def get_framework_status(self): return {"initialized": True, "mock": True}
    
    class FrameworkConfig:
        def __init__(self, **kwargs): pass
    
    class AgentType:
        CUSTOMER_SERVICE = "customer_service"
        EQUIPMENT_SPECIALIST = "equipment_specialist"
        TECHNICAL_SUPPORT = "technical_support"
        MAINTENANCE_PLANNER = "maintenance_planner"
        ANALYTICS_SPECIALIST = "analytics_specialist"

logger = logging.getLogger(__name__)

@dataclass
class SemanticAnalysisResult:
    """Result of semantic analysis"""
    success: bool
    analysis_id: str
    sentiment: Dict[str, Any]
    intent: Dict[str, Any]
    urgency_score: float
    keywords: List[str]
    hvac_concepts: List[str]
    customer_insights: Dict[str, Any]
    processing_time: float
    timestamp: datetime

class SemanticFrameworkManager:
    """
    🧠 Semantic Framework Integration Manager
    
    Manages the integration between Gobeklitepe semantic framework
    and the Flet interface, providing unified access to semantic
    analysis capabilities for HVAC CRM operations.
    """
    
    def __init__(self):
        self.framework: Optional[HVACSemanticFramework] = None
        self.is_initialized = False
        self.config = self._create_framework_config()
        self.analysis_cache = {}
        self.performance_metrics = {
            "total_analyses": 0,
            "successful_analyses": 0,
            "average_response_time": 0.0,
            "cache_hit_rate": 0.0
        }
        logger.info("🧠 Semantic Framework Manager initialized")
    
    def _create_framework_config(self) -> FrameworkConfig:
        """Create framework configuration"""
        return FrameworkConfig(
            # Weaviate configuration
            weaviate_url="http://localhost:8080",
            weaviate_api_key=None,
            
            # Semantic models
            semantic_models={
                "sentence_transformer": "all-MiniLM-L6-v2",
                "spacy_model": "en_core_web_sm", 
                "sentiment_model": "cardiffnlp/twitter-roberta-base-sentiment-latest"
            },
            
            # Agent configuration
            max_concurrent_agents=10,
            agent_timeout=300,
            
            # Integration settings
            enable_real_time=True,
            enable_monitoring=True,
            enable_persistence=True,
            
            # HVAC specific
            hvac_equipment_brands=["LG", "Daikin", "Mitsubishi", "Carrier", "Trane", "Fujitsu"],
            hvac_service_types=["installation", "maintenance", "repair", "inspection", "emergency"]
        )
    
    async def initialize(self) -> bool:
        """Initialize the semantic framework"""
        try:
            logger.info("🚀 Initializing Gobeklitepe semantic framework...")
            
            self.framework = HVACSemanticFramework(self.config)
            success = await self.framework.initialize()
            
            if success:
                self.is_initialized = True
                logger.info("✅ Semantic framework initialized successfully")
                return True
            else:
                logger.error("❌ Semantic framework initialization failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Semantic framework initialization error: {e}")
            # Create mock framework for development
            self.framework = HVACSemanticFramework(self.config)
            self.is_initialized = True
            logger.warning("⚠️ Using mock semantic framework for development")
            return True
    
    async def analyze_customer_communication(self, 
                                           communication_text: str,
                                           customer_id: Optional[str] = None,
                                           communication_type: str = "email") -> SemanticAnalysisResult:
        """
        📧 Analyze customer communication with semantic intelligence
        """
        start_time = datetime.now()
        
        try:
            if not self.is_initialized:
                await self.initialize()
            
            # Check cache first
            cache_key = f"{hash(communication_text)}_{communication_type}"
            if cache_key in self.analysis_cache:
                cached_result = self.analysis_cache[cache_key]
                self._update_metrics(True, 0.001, True)  # Cache hit
                return cached_result
            
            # Perform semantic analysis
            result = await self.framework.analyze_customer_communication(
                communication_text=communication_text,
                customer_id=customer_id,
                communication_type=communication_type
            )
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # Create structured result
            analysis_result = SemanticAnalysisResult(
                success=result.get("success", False),
                analysis_id=result.get("result_id", f"analysis_{datetime.now().timestamp()}"),
                sentiment=result.get("semantic_analysis", {}).get("sentiment", {"label": "neutral", "score": 0.5}),
                intent=result.get("semantic_analysis", {}).get("intent", {"label": "general", "confidence": 0.5}),
                urgency_score=result.get("semantic_analysis", {}).get("urgency_score", 0.3),
                keywords=result.get("semantic_analysis", {}).get("keywords", []),
                hvac_concepts=result.get("semantic_analysis", {}).get("hvac_concepts", []),
                customer_insights=self._generate_customer_insights(result),
                processing_time=processing_time,
                timestamp=datetime.now()
            )
            
            # Cache result
            self.analysis_cache[cache_key] = analysis_result
            
            # Update metrics
            self._update_metrics(True, processing_time, False)
            
            logger.info(f"✅ Communication analysis completed in {processing_time:.2f}s")
            return analysis_result
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"❌ Communication analysis failed: {e}")
            
            # Return mock result for development
            mock_result = SemanticAnalysisResult(
                success=False,
                analysis_id=f"mock_{datetime.now().timestamp()}",
                sentiment={"label": "neutral", "score": 0.5},
                intent={"label": "service_request", "confidence": 0.7},
                urgency_score=0.4,
                keywords=["hvac", "service", "maintenance"],
                hvac_concepts=["air_conditioning", "maintenance"],
                customer_insights={"mock": True, "error": str(e)},
                processing_time=processing_time,
                timestamp=datetime.now()
            )
            
            self._update_metrics(False, processing_time, False)
            return mock_result
    
    async def search_hvac_knowledge(self, 
                                  query: str,
                                  search_type: str = "hybrid",
                                  limit: int = 10) -> Dict[str, Any]:
        """
        🔍 Search HVAC knowledge base with semantic intelligence
        """
        try:
            if not self.is_initialized:
                await self.initialize()
            
            result = await self.framework.search_hvac_knowledge(
                query=query,
                search_type=search_type,
                limit=limit
            )
            
            logger.info(f"🔍 Knowledge search completed: {result.get('result_count', 0)} results")
            return result
            
        except Exception as e:
            logger.error(f"❌ Knowledge search failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "query": query,
                "results": [],
                "mock": True
            }
    
    async def process_equipment_data(self, equipment_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        🔧 Process equipment data with semantic enhancement
        """
        try:
            if not self.is_initialized:
                await self.initialize()
            
            result = await self.framework.process_equipment_data(equipment_info)
            
            logger.info(f"🔧 Equipment processing completed: {result.get('result_id', 'unknown')}")
            return result
            
        except Exception as e:
            logger.error(f"❌ Equipment processing failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "equipment_info": equipment_info,
                "mock": True
            }
    
    def get_framework_status(self) -> Dict[str, Any]:
        """Get comprehensive framework status"""
        if self.framework:
            framework_status = self.framework.get_framework_status()
        else:
            framework_status = {"initialized": False}
        
        return {
            **framework_status,
            "manager_initialized": self.is_initialized,
            "cache_size": len(self.analysis_cache),
            "performance_metrics": self.performance_metrics,
            "timestamp": datetime.now().isoformat()
        }
    
    def _generate_customer_insights(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """Generate customer insights from analysis result"""
        semantic_analysis = analysis_result.get("semantic_analysis", {})
        
        insights = {
            "satisfaction_level": self._calculate_satisfaction(semantic_analysis),
            "service_urgency": self._classify_urgency(semantic_analysis.get("urgency_score", 0.3)),
            "equipment_focus": self._extract_equipment_focus(semantic_analysis.get("hvac_concepts", [])),
            "recommended_actions": self._generate_recommendations(semantic_analysis),
            "risk_indicators": self._identify_risks(semantic_analysis)
        }
        
        return insights
    
    def _calculate_satisfaction(self, semantic_analysis: Dict[str, Any]) -> str:
        """Calculate customer satisfaction level"""
        sentiment = semantic_analysis.get("sentiment", {})
        sentiment_label = sentiment.get("label", "neutral").lower()
        sentiment_score = sentiment.get("score", 0.5)
        
        if sentiment_label == "positive" and sentiment_score > 0.7:
            return "high"
        elif sentiment_label == "negative" and sentiment_score > 0.7:
            return "low"
        else:
            return "medium"
    
    def _classify_urgency(self, urgency_score: float) -> str:
        """Classify urgency level"""
        if urgency_score >= 0.9:
            return "critical"
        elif urgency_score >= 0.7:
            return "high"
        elif urgency_score >= 0.4:
            return "medium"
        else:
            return "low"
    
    def _extract_equipment_focus(self, hvac_concepts: List[str]) -> List[str]:
        """Extract equipment focus from HVAC concepts"""
        equipment_keywords = ["air_conditioning", "heating", "ventilation", "hvac_system", "compressor", "filter"]
        return [concept for concept in hvac_concepts if any(keyword in concept.lower() for keyword in equipment_keywords)]
    
    def _generate_recommendations(self, semantic_analysis: Dict[str, Any]) -> List[str]:
        """Generate action recommendations"""
        recommendations = []
        
        urgency_score = semantic_analysis.get("urgency_score", 0.3)
        sentiment = semantic_analysis.get("sentiment", {})
        hvac_concepts = semantic_analysis.get("hvac_concepts", [])
        
        if urgency_score > 0.7:
            recommendations.append("Schedule immediate technician visit")
        
        if sentiment.get("label") == "negative":
            recommendations.append("Follow up with customer satisfaction survey")
        
        if any("maintenance" in concept.lower() for concept in hvac_concepts):
            recommendations.append("Review maintenance schedule")
        
        if any("emergency" in concept.lower() for concept in hvac_concepts):
            recommendations.append("Activate emergency response protocol")
        
        return recommendations or ["Monitor customer communication"]
    
    def _identify_risks(self, semantic_analysis: Dict[str, Any]) -> List[str]:
        """Identify potential risks"""
        risks = []
        
        urgency_score = semantic_analysis.get("urgency_score", 0.3)
        sentiment = semantic_analysis.get("sentiment", {})
        hvac_concepts = semantic_analysis.get("hvac_concepts", [])
        
        if urgency_score > 0.8 and sentiment.get("label") == "negative":
            risks.append("Customer churn risk")
        
        if any("failure" in concept.lower() for concept in hvac_concepts):
            risks.append("Equipment failure risk")
        
        if any("emergency" in concept.lower() for concept in hvac_concepts):
            risks.append("Safety risk")
        
        return risks
    
    def _update_metrics(self, success: bool, processing_time: float, cache_hit: bool):
        """Update performance metrics"""
        self.performance_metrics["total_analyses"] += 1
        
        if success:
            self.performance_metrics["successful_analyses"] += 1
        
        # Update average response time
        total = self.performance_metrics["total_analyses"]
        current_avg = self.performance_metrics["average_response_time"]
        self.performance_metrics["average_response_time"] = (
            (current_avg * (total - 1) + processing_time) / total
        )
        
        # Update cache hit rate
        if cache_hit:
            cache_hits = self.performance_metrics.get("cache_hits", 0) + 1
            self.performance_metrics["cache_hits"] = cache_hits
            self.performance_metrics["cache_hit_rate"] = cache_hits / total
    
    def clear_cache(self):
        """Clear analysis cache"""
        self.analysis_cache.clear()
        logger.info("🧹 Analysis cache cleared")