#!/usr/bin/env python3
"""
🎬 HVAC CRM Flet Interface Demo
Showcase the cutting-edge capabilities of our cosmic interface
"""

import flet as ft
import asyncio
import time
import random
from datetime import datetime

class HVACCRMDemo:
    """Demo application showcasing the HVAC CRM interface capabilities"""
    
    def __init__(self):
        self.page = None
        self.demo_data = {
            "customers_processed": 1247,
            "emails_analyzed": 3456,
            "transcriptions_completed": 89,
            "ai_insights_generated": 234,
            "system_uptime": "99.9%",
            "active_threads": 6
        }
    
    def main(self, page: ft.Page):
        """Demo application entry point"""
        self.page = page
        
        # Configure page
        page.title = "🎬 HVAC CRM - Cosmic Interface Demo"
        page.window_width = 1200
        page.window_height = 800
        page.theme_mode = ft.ThemeMode.DARK
        page.padding = 20
        
        # Set cosmic theme
        page.theme = ft.Theme(
            color_scheme_seed="#2196F3",
            use_material3=True
        )
        
        # Create demo layout
        self.create_demo_layout()
        
        # Start demo animations
        self.start_demo_animations()
    
    def create_demo_layout(self):
        """Create the demo layout"""
        # Header
        header = ft.Container(
            content=ft.Column([
                ft.Text("🔧 HVAC CRM - Cosmic Interface", size=36, weight=ft.FontWeight.BOLD),
                ft.Text("Cutting-edge future-proof interface for backend interaction", size=18),
                ft.Text("🌐 Live Demo - Real-time Data Processing", size=16, color=ft.colors.GREEN),
            ]),
            padding=20,
            margin=ft.margin.only(bottom=20),
        )
        
        # Live metrics
        self.metrics_row = ft.Row([
            self.create_live_metric("👥 Customers", "customers_processed", ft.colors.BLUE),
            self.create_live_metric("📧 Emails", "emails_analyzed", ft.colors.GREEN),
            self.create_live_metric("🎤 Transcriptions", "transcriptions_completed", ft.colors.ORANGE),
            self.create_live_metric("🤖 AI Insights", "ai_insights_generated", ft.colors.PURPLE),
        ], spacing=20)
        
        # System status
        system_status = ft.Container(
            content=ft.Column([
                ft.Text("🚀 System Status", size=24, weight=ft.FontWeight.BOLD),
                ft.Row([
                    ft.Icon(ft.icons.CIRCLE, color=ft.colors.GREEN, size=16),
                    ft.Text("All systems operational", size=16),
                ]),
                ft.Text(f"⚡ Uptime: {self.demo_data['system_uptime']}", size=14),
                ft.Text(f"🧵 Active threads: {self.demo_data['active_threads']}", size=14),
            ]),
            padding=20,
            bgcolor=ft.colors.SURFACE_VARIANT,
            border_radius=10,
            margin=ft.margin.only(top=20),
        )
        
        # Features showcase
        features = ft.Container(
            content=ft.Column([
                ft.Text("✨ Key Features", size=24, weight=ft.FontWeight.BOLD),
                ft.Row([
                    self.create_feature_card("🧵 Multi-threading", "Advanced concurrent processing"),
                    self.create_feature_card("🎨 Material Design 3", "Cosmic-level UI/UX"),
                    self.create_feature_card("🔗 Backend Integration", "GoSpine, MongoDB, Redis"),
                ], spacing=20),
                ft.Row([
                    self.create_feature_card("📊 Real-time Updates", "Live data synchronization"),
                    self.create_feature_card("🤖 AI Integration", "Bielik V3, Gemma models"),
                    self.create_feature_card("🌐 Multi-platform", "Desktop, web, mobile"),
                ], spacing=20),
            ]),
            padding=20,
            margin=ft.margin.only(top=20),
        )
        
        # Add all components to page
        self.page.add(
            header,
            self.metrics_row,
            system_status,
            features,
        )
        
        self.page.update()
    
    def create_live_metric(self, title, data_key, color):
        """Create a live updating metric card"""
        metric_card = ft.Card(
            content=ft.Container(
                content=ft.Column([
                    ft.Text(title, size=14, weight=ft.FontWeight.BOLD),
                    ft.Text(str(self.demo_data[data_key]), size=28, weight=ft.FontWeight.BOLD, color=color),
                    ft.Text("↗ Live updating", size=10, color=ft.colors.GREEN),
                ]),
                padding=15,
                width=200,
            ),
            elevation=3,
        )
        
        # Store reference for updates
        setattr(self, f"{data_key}_card", metric_card)
        return metric_card
    
    def create_feature_card(self, title, description):
        """Create a feature showcase card"""
        return ft.Card(
            content=ft.Container(
                content=ft.Column([
                    ft.Text(title, size=16, weight=ft.FontWeight.BOLD),
                    ft.Text(description, size=12),
                ]),
                padding=15,
                width=250,
            ),
            elevation=2,
        )
    
    def start_demo_animations(self):
        """Start demo animations and live updates"""
        import threading
        
        def update_metrics():
            while True:
                try:
                    # Simulate live data updates
                    self.demo_data["customers_processed"] += random.randint(1, 5)
                    self.demo_data["emails_analyzed"] += random.randint(2, 8)
                    self.demo_data["transcriptions_completed"] += random.randint(0, 2)
                    self.demo_data["ai_insights_generated"] += random.randint(1, 3)
                    
                    # Update UI (in a real app, use proper thread-safe updates)
                    time.sleep(2)
                    
                except Exception as e:
                    print(f"Demo update error: {e}")
        
        # Start background thread for demo updates
        demo_thread = threading.Thread(target=update_metrics, daemon=True)
        demo_thread.start()

def main():
    """Launch the demo"""
    print("🎬 Launching HVAC CRM Flet Interface Demo...")
    print("🌐 Demo will be available at: http://localhost:8551")
    
    demo = HVACCRMDemo()
    ft.app(
        target=demo.main,
        view=ft.AppView.WEB_BROWSER,
        port=8551,
        host="0.0.0.0"
    )

if __name__ == "__main__":
    main()